package isimado.model.function.test;

import imodel.JSimPro.dataTypes.*;
import imodel.jsim.function.common.FAAS_Antenna;
import imodel.jsim.function.common.StandardAntennaPatternAntenna;
import jsim.pro.sensor.antenna.AesStandardAntennaPattern;

public class AntennaPatternUtils {
    public static AesStandardAntennaPattern newCircleAntennaPatternAntenna(CircularPatternVO pattern){
        AntennaPatternVO patternVO = AntennaPatternVO.newBuilder()
                .setPatternParams(AntennaPatternVariantVO.newBuilder()
                        .setCircularPattern(pattern)
                        .build())
                .build();
        AesStandardAntennaPattern antennaPattern = new AesStandardAntennaPattern("circular_pattern");
        antennaPattern.ProcessInput(patternVO);
        return antennaPattern;
    }


    public static AesStandardAntennaPattern newCosecantSquaredPatternAntenna(CosecantPatternVO pattern){
        AntennaPatternVO patternVO = AntennaPatternVO.newBuilder()
                .setPatternParams(AntennaPatternVariantVO.newBuilder()
                        .setCosecantSquaredPattern(pattern)
                        .build())
                .build();
        AesStandardAntennaPattern antennaPattern = new AesStandardAntennaPattern("cosecant_squared_pattern");
        antennaPattern.ProcessInput(patternVO);
        return antennaPattern;
    }

    public static AesStandardAntennaPattern newRectangularPattern(RectangularPatternVO pattern){
        AntennaPatternVO patternVO = AntennaPatternVO.newBuilder()
                .setPatternParams(AntennaPatternVariantVO.newBuilder()
                        .setRectangularPattern(pattern)
                        .build())
                .build();
        AesStandardAntennaPattern antennaPattern = new AesStandardAntennaPattern("cosecant_squared_pattern");
        antennaPattern.ProcessInput(patternVO);
        return antennaPattern;
    }

    public static void main(String[] args){
        AesStandardAntennaPattern jammerPattern = newCircleAntennaPatternAntenna(CircularPatternVO.newBuilder()
                .setPeakGain(30d)
                .setBeamwidth(5d)
                .build());
        FAAS_Antenna jammerAntenna = new StandardAntennaPatternAntenna(-180,180,
                -90,90,jammerPattern);

        for (int azimuth = -180; azimuth <= 179; azimuth+=5) {
            double azimuthRad = Math.toRadians(azimuth);
            System.out.println(azimuth+":"+jammerAntenna.getRcvrGain(9.2*10e6, azimuthRad, 0.15));
        }
    }
}
