package isimado.model.function.share.entity;

import io.swagger.annotations.ApiModelProperty;
import isimado.jsim.data_type.AntennaParameter;
import isimado.jsim.data_type.AntennaPatternVariant;
import isimado.jsim.data_type.LocationLLA;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 计算电侦设备2D包络
 */
@Data
public class ESM2DRangeComputeDTO {

    @ApiModelProperty(value = "电侦设备所在平台的经纬高", required = true)
    private LocationLLA locationLLA;

    @ApiModelProperty(value = "电侦设备所在平台的朝向，度数", required = true)
    private double heading;

    @ApiModelProperty(value = "电侦设备所在平台的俯仰角，度数", required = true)
    private double pitch;

    @ApiModelProperty("电侦设备天线参数")
    private AntennaParameter antennaParameter;

    @ApiModelProperty(value = "发射机功率(W)", required = true)
    private double xmtrPower;

    @ApiModelProperty(value = "发射机天线增益", required = true)
    private double gain;

    @ApiModelProperty(value = "发射机全向理想增益(HZ)", required = true)
    private double freqHz;

    @ApiModelProperty(value="目标高度，默认值1000")
    private double targetHeight = 1000;

    @ApiModelProperty(value = "电侦设备热噪声",required = true)
    private double noisePower;

    @ApiModelProperty(value = "SNR阈值",required = true)
    private double detectionThread;

    @ApiModelProperty(value = "最大值",required = true)
    private double maxRange;

    @ApiModelProperty(value = "干扰机状态",required = false)
    private List<JammerDTO> jammers = new ArrayList<>();
}


