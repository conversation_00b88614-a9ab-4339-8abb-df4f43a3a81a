package isimado.model.function.domain;

import imodel.JSimPro.dataTypes.*;
import isimado.jsim.data_type.AntennaPatternVariant;
import isimado.jsim.data_type.CircularPattern;
import isimado.jsim.data_type.CosecantPattern;
import isimado.jsim.data_type.RectangularPattern;
import isimado.jsim.data_type.UniformPattern;
import jsim.pro.sensor.antenna.AesStandardAntennaPattern;

public class AntennaPatternUtils {
    public static AesStandardAntennaPattern newPatternAntenna(AntennaPatternVariant variant){
        if (variant.getCircular_pattern() != null){
            return newCircleAntennaPatternAntenna(variant.getCircular_pattern());
        }
        if (variant.getCosecant_squared_pattern() != null){
            return newCosecantSquaredPatternAntenna(variant.getCosecant_squared_pattern());
        }
        if (variant.getRectangular_pattern() != null){
            return newRectangularPattern(variant.getRectangular_pattern());
        }
        if (variant.getUniform_pattern() != null){
            return newUniformPattern(variant.getUniform_pattern());
        }
        return null;
    }


    public static AesStandardAntennaPattern newCircleAntennaPatternAntenna(CircularPattern pattern){
        AntennaPatternVO patternVO = AntennaPatternVO.newBuilder()
                .setPatternParams(AntennaPatternVariantVO.newBuilder()
                        .setCircularPattern(CircularPatternVO.newBuilder()
                                .setBeamwidth(pattern.getBeamwidth())
                                .setPeakGain(pattern.getPeak_gain())
                                .build())
                        .build())
                .build();
        AesStandardAntennaPattern antennaPattern = new AesStandardAntennaPattern("circular_pattern");
        antennaPattern.ProcessInput(patternVO);
        return antennaPattern;
    }


    public static AesStandardAntennaPattern newCosecantSquaredPatternAntenna(CosecantPattern pattern){
        AntennaPatternVO patternVO = AntennaPatternVO.newBuilder()
                .setPatternParams(AntennaPatternVariantVO.newBuilder()
                        .setCosecantSquaredPattern(CosecantPatternVO.newBuilder()
                                .setPeakGain(pattern.getPeak_gain())
                                .setAzimuthBeamwidth(pattern.getAzimuth_beamwidth())
                                .setElevationBeamwidth(pattern.getElevation_beamwidth())
                                .setMaximumElevationForCsc2(pattern.getMaximum_elevation_for_csc2())
                                .setMinimumElevationForPeakGain(pattern.getMinimum_elevation_for_peak_gain())
                                .build())
                        .build())
                .build();
        AesStandardAntennaPattern antennaPattern = new AesStandardAntennaPattern("cosecant_squared_pattern");
        antennaPattern.ProcessInput(patternVO);
        return antennaPattern;
    }

    public static AesStandardAntennaPattern newRectangularPattern(RectangularPattern pattern){
        AntennaPatternVO patternVO = AntennaPatternVO.newBuilder()
                .setPatternParams(AntennaPatternVariantVO.newBuilder()
                        .setRectangularPattern(RectangularPatternVO.newBuilder()
                                .setPeakGain(pattern.getPeak_gain())
                                .setAzimuthBeamwidth(pattern.getAzimuth_beamwidth())
                                .setElevationBeamwidth(pattern.getElevation_beamwidth())
                                .setMinimumGain(pattern.getMinimum_gain())
                                .build())
                        .build())
                .build();
        AesStandardAntennaPattern antennaPattern = new AesStandardAntennaPattern("cosecant_squared_pattern");
        antennaPattern.ProcessInput(patternVO);
        return antennaPattern;
    }

    public static AesStandardAntennaPattern newUniformPattern(UniformPattern pattern){
        AntennaPatternVO patternVO = AntennaPatternVO.newBuilder()
                .setPatternParams(AntennaPatternVariantVO.newBuilder()
                        .setUniformPattern(UniformPatternVO.newBuilder()
                                .setPeakGain(pattern.getPeak_gain())
                                .setAzimuthBeamwidth(pattern.getAzimuth_beamwidth())
                                .setElevationBeamwidth(pattern.getElevation_beamwidth())
                                .build())
                        .build())
                .build();
        AesStandardAntennaPattern antennaPattern = new AesStandardAntennaPattern("uniform_pattern");
        antennaPattern.ProcessInput(patternVO);
        return antennaPattern;
    }
}
