package isimado.model.function.share.entity;



public class GisTifFileInfo implements Comparable<GisTifFileInfo> {

    String fileName;
    double minLon;
    double minLat;
    double maxLon;
    double maxLat;

    public GisTifFileInfo(String fileName, double minLon, double minLat, double maxLon, double maxLat) {
        this.fileName = fileName;
        this.minLon = minLon;
        this.minLat = minLat;
        this.maxLon = maxLon;
        this.maxLat = maxLat;
    }
    @Override
    public int compareTo(GisTifFileInfo o) {
        return Double.compare(minLon, o.minLon);
    }
}
