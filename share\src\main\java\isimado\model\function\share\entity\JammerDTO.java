package isimado.model.function.share.entity;

import io.swagger.annotations.ApiModelProperty;
import isimado.jsim.data_type.AntennaParameter;
import isimado.jsim.data_type.AntennaPatternVariant;
import isimado.jsim.data_type.LocationLLA;
import lombok.Data;

/**
 * 计算电台2D包络
 */
@Data
public class JammerDTO {

    @ApiModelProperty(value = "电台所在平台的经纬高", required = true)
    private LocationLLA locationLLA;

    @ApiModelProperty(value = "电台所在平台的朝向，度数", required = true)
    private double heading;

    @ApiModelProperty(value = "电台所在平台的俯仰角，度数", required = true)
    private double pitch;

    @ApiModelProperty("电台天线参数")
    private AntennaParameter antennaParameter;

    @ApiModelProperty(value = "发射机功率(W)", required = true)
    private double xmtrPower;

    @ApiModelProperty(value = "发射机天线方向图参数", required = true)
    private AntennaPatternVariant antennaPattern;

    @ApiModelProperty(value = "发射机工作频率(HZ)", required = true)
    private double freqHz;

    @ApiModelProperty(value = "带宽效能", required = true)
    private double bandWidthEffect;

    @ApiModelProperty(value = "最大值",required = true)
    private double maxRange;
}


