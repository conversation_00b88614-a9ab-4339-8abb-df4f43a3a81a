package isimado.model.function.domain.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import isimado.framework.base.Resp;
import isimado.framework.util.JsonUtils;
import isimado.model.function.domain.ship_rcs.RadarDetectRequest;
import isimado.model.function.domain.ship_rcs.RadarDetectResponse;
import isimado.model.function.domain.ship_rcs.RadarStatus;
import isimado.model.function.domain.ship_rcs.SituationResponse;
import isimado.model.function.domain.service.ShipRCSService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "小艇接口")
@ApiSupport(order = 1, author = "clyu")
@RestController
@Slf4j
@RequestMapping("ship_rcs")
public class ShipRcsComputeController {

    @Resource
    private ShipRCSService shipRcsService;

    @ApiOperation(value = "探测结果")
    @ApiOperationSupport(order = 100)
    @PostMapping("attemptDetected")
    public Resp<RadarDetectResponse> attemptDetected(@RequestBody RadarDetectRequest dto) {
        RadarDetectResponse response = new RadarDetectResponse();
        response.setTracks(shipRcsService.attemptDetect(dto));
        return Resp.success(response);
    }

    @ApiOperation(value = "态势展示")
    @ApiOperationSupport(order = 100)
    @PostMapping("getSituation")
    public Resp<SituationResponse> getSituation() {
        SituationResponse response = JsonUtils.parse(shipRcsService.getSituation(), SituationResponse.class);
//        response.setTimeStamp(System.currentTimeMillis());
        return Resp.success(response);
    }

    @ApiOperation(value = "雷达包络2D")
    @ApiOperationSupport(order = 100)
    @PostMapping("2D")
    public Resp<double[][]> range2D(@RequestBody RadarStatus dto) {
        return Resp.success(shipRcsService.compute2DRange(dto));
    }




    @ApiOperation(value = "雷达包络3D")
    @ApiOperationSupport(order = 100)
    @PostMapping("3D")
    public Resp<double[][]> range3D(@RequestBody RadarStatus dto) {
        return Resp.success(shipRcsService.compute3DRange(dto));
    }
}
