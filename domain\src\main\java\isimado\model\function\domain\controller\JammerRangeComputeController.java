package isimado.model.function.domain.controller;


import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import isimado.framework.base.Resp;
import isimado.model.function.domain.service.JammerFunctionService;
import isimado.model.function.share.entity.Jammer2DRangeComputeDTO;
import isimado.model.function.share.entity.Jammer3DRangeComputeDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "干扰包络")
@ApiSupport(order = 1, author = "clyu")
@RestController
@Slf4j
@RequestMapping("jammer")
public class JammerRangeComputeController {

    @Resource
    private JammerFunctionService functionService;

    @ApiOperation(value = "干扰包络2D")
    @ApiOperationSupport(order = 100)
    @PostMapping("2D")
    public Resp<double[][]> range2D(@RequestBody Jammer2DRangeComputeDTO dto) {
        return Resp.success(functionService.compute2DRange(dto));
    }




    @ApiOperation(value = "干扰包络3D")
    @ApiOperationSupport(order = 100)
    @PostMapping("3D")
    public Resp<double[][]> range3D(@RequestBody Jammer3DRangeComputeDTO dto) {
        return Resp.success(functionService.compute3DRange(dto));
    }






}
