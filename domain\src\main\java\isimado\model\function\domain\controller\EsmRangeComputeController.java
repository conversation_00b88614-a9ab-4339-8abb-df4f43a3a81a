package isimado.model.function.domain.controller;


import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import isimado.framework.base.Resp;
import isimado.model.function.domain.service.ESMFunctionService;
import isimado.model.function.share.entity.ESM2DRangeComputeDTO;
import isimado.model.function.share.entity.ESM3DRangeComputeDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "电侦包络")
@ApiSupport(order = 1, author = "clyu")
@RestController
@Slf4j
@RequestMapping("esm")
public class EsmRangeComputeController {

    @Resource
    private ESMFunctionService functionService;

    @ApiOperation(value = "电侦包络2D")
    @ApiOperationSupport(order = 100)
    @PostMapping("2D")
    public Resp<double[][]> range2D(@RequestBody ESM2DRangeComputeDTO dto) {
        return Resp.success(functionService.compute2DRange(dto));
    }




    @ApiOperation(value = "电侦包络3D")
    @ApiOperationSupport(order = 100)
    @PostMapping("3D")
    public Resp<double[][]> range3D(@RequestBody ESM3DRangeComputeDTO dto) {
        return Resp.success(functionService.compute3DRange(dto));
    }






}
