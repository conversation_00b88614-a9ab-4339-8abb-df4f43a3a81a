package isimado.model.function.test;

import imodel.JSimPro.dataTypes.CircularPatternVO;
import imodel.jsim.function.common.*;
import jsim.pro.enviorment.AesEnvironment;
import jsim.pro.sensor.antenna.AesStandardAntennaPattern;
import jsim.pro.utils.UtAtmosphere;

import static isimado.model.function.test.AntennaPatternUtils.newCircleAntennaPatternAntenna;

public class TestRadar2DRange {

    public static void main(String[] args) {
        AesStandardAntennaPattern pattern = newCircleAntennaPatternAntenna(CircularPatternVO.newBuilder()
                .setPeakGain(25d)
                .setBeamwidth(1d)
                .build());
        FAAS_Antenna faasAntenna = new StandardAntennaPatternAntenna(-180,180,
                -90,90,pattern);
        Target target = new Target();
        target.setRcs(10);
        FAAS_ITU_Attenuation faasItuAttenuation = new FAAS_ITU_Attenuation(UtAtmosphere.CreateNewAtmosphereTable(),
                9000*10e6, 0, new AesEnvironment());
        FAASTwoWayInteraction faasRadar = new FAASTwoWayInteraction(840000,9200*10e6,1.0000000000000002e-14,
                faasAntenna,1.5848931924611136,new double[]{23.80525,120.240896,5000},
                Math.toRadians(0),
                Math.toRadians(0),10,500000
                ,target,faasItuAttenuation);
        JammerList jammerList = new JammerList();


        AesStandardAntennaPattern jammerPattern = newCircleAntennaPatternAntenna(CircularPatternVO.newBuilder()
                .setPeakGain(20d)
                .setBeamwidth(1d)
                .build());
        FAAS_Antenna jammerAntenna = new StandardAntennaPatternAntenna(-180,180,
                -90,90,jammerPattern);
        jammerList.addJammer(new FAASJammerInteraction(100,9200*1e6,
                jammerAntenna,new double[]{24.80525,120.240896,5000}, 0,0,
                faasRadar,1d));

        double[][] rangeData = faasRadar.compute2DRange(5000,jammerList);
        for (double[] rangeDatum : rangeData) {
            StringBuilder sb = new StringBuilder("[");
            for (double v : rangeDatum) {
                sb.append(v).append(",");
            }
            String result = sb.substring(0, sb.length() - 1);
            System.out.println(result+"]");
        }

    }
}
