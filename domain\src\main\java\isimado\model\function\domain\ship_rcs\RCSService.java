package isimado.model.function.domain.ship_rcs;

import cn.hutool.json.JSONUtil;
import imodel.JSimPro.dataTypes.*;
import jsim.pro.sensor.signature.AesStandardRadarSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

@Service
public class RCSService {

    @Value("${service.ship_rcs.rcs.top}")
    String top = "";
    @Value("${service.ship_rcs.rcs.left}")
    String left = "";
    @Value("${service.ship_rcs.rcs.front}")
    String front = "";
    @Value("${service.ship_rcs.rcs.backend}")
    String backend = "";

    AesStandardRadarSignature aesStandardRadarSignature;

    AesRadarSignatureShareDataVO mSignaturePtr;
    @PostConstruct
    public void init() {
        List<String> inline_table = new ArrayList<>();

        inline_table.add("state default");
        inline_table.add("inline_table dBsm 6 4");
        inline_table.add("-90 -30  30  90");
        inline_table.add("-180 top  backend backend  top");
        inline_table.add("-160 top  left left  top");
        inline_table.add("-30 top  front  front  top");
        inline_table.add("30 top  front  front top");
        inline_table.add("160 top left left top");
        inline_table.add("180 top backend  backend top");
        inline_table.add("end_inline_table");
        mSignaturePtr = AesRadarSignatureShareDataVO.newBuilder()
                .setMSates(new ArrayList<>() {{
                    add(AesRadarSignatureShareDataStateVO.newBuilder()
                            .setMPolarization(new ArrayList<>())
                            .build());
                }})
                .setMMonoStaticSigDefined(true)
                .build();
        AesRadarSignatureVO signatureVO = AesRadarSignatureVO.newBuilder()
                .setMSignaturePtr(mSignaturePtr)
                .build();
        String jsonStr = JSONUtil.toJsonStr(inline_table);
        jsonStr = jsonStr.replace("top", top);
        jsonStr = jsonStr.replace("left", left);
        jsonStr = jsonStr.replace("backend", backend);
        jsonStr = jsonStr.replace("front", front);

        RadarSignatureVO aInput = RadarSignatureVO.newBuilder()
                .setInlineTable(jsonStr)
                .setState(new ArrayList<>(){{
                    add(AesRadarSignatureStateVO.newBuilder()
                            .setStateName("default")
                            .setPolarization("")
                            .setFrequencyLimit("")
                            .build());
                }})
                .build();
        aesStandardRadarSignature = new AesStandardRadarSignature();
        aesStandardRadarSignature.ProcessInput(signatureVO, aInput);
    }

    public double getSignature(String aStateId,
                               int aPolarization,
                               double aFrequency,
                               double aTgtToXmtrAz,
                               double aTgtToXmtrEl,
                               double aTgtToRcvrAz,
                               double aTgtToRcvrEl
    ) {

        return aesStandardRadarSignature.getSignature(aStateId,
                aPolarization,
                aFrequency,
                aTgtToXmtrAz,
                aTgtToXmtrEl,
                aTgtToRcvrAz,
                aTgtToRcvrEl,
                mSignaturePtr);
    }
}
