package isimado.model.function.domain.ship_rcs;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RadarStatus {
    @ApiModelProperty(value = "唯一ID", required = true)
    private String entityId;

    @ApiModelProperty(value = "平台的经纬高", required = true)
    private LocationLLA locationLLA;

    @ApiModelProperty(value = "水平扫描角度起始值，东北天坐标系，0度为正北，顺时针为正，逆时针为负,默认值为-180")
    private double aziScanStart = -180;

    @ApiModelProperty(value = "水平扫描角度结束值，东北天坐标系，0度为正北，顺时针为正，逆时针为负，默认值为180")
    private double aziScanEnd = 180;
}
