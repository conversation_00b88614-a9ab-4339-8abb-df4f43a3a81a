package isimado.model.function.domain.context;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/3 10:27
 */
@Slf4j
@Configuration
public class SwaggerConfiguration implements WebMvcConfigurer {

    public static final String TOKEN = "token";

    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        registry.addViewController("/")
                .setViewName("forward:" + "/doc.html");
    }

    @Bean
    public Docket createRestApi() {
        String title = "模型算法工具";
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(new ApiInfoBuilder()
                        .title(title)
                        .description("模型算法关的接口")
                        .version("1.0")
                        .build())
                .select()
                .paths(path -> !PathSelectors.regex("/error.*").test(path))
                .paths(PathSelectors.any())
                .build()
                .globalOperationParameters(getParameterList());
    }

    private List<Parameter> getParameterList() {
//        Docket docket = createRestApi();

        ParameterBuilder tokenBuilder = new ParameterBuilder();

//        Documentation d = new Documentation()

        Parameter parameter = tokenBuilder.name(TOKEN).description("token令牌")
                .modelRef(new ModelRef("string"))
                .parameterType("header")
                .required(false).build();

        return List.of(parameter);
    }
}
