package isimado.model.function.share.entity;

import io.swagger.annotations.ApiModelProperty;
import isimado.jsim.data_type.AntennaParameter;
import isimado.jsim.data_type.AntennaPatternVariant;
import isimado.jsim.data_type.LocationLLA;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 计算干扰机2D包络
 */
@Data
public class Jammer2DRangeComputeDTO {

    @ApiModelProperty(value = "干扰机所在平台的经纬高", required = true)
    private LocationLLA locationLLA;

    @ApiModelProperty(value = "干扰机所在平台的朝向，度数", required = true)
    private double heading;

    @ApiModelProperty(value = "干扰机所在平台的俯仰角，度数", required = true)
    private double pitch;

    @ApiModelProperty("干扰机天线参数")
    private AntennaParameter antennaParameter;

    @ApiModelProperty(value = "发射机功率(W)", required = true)
    private double xmtrPower;

    @ApiModelProperty(value = "发射机天线方向图参数", required = true)
    private AntennaPatternVariant antennaPattern;

    @ApiModelProperty(value = "发射机全向理想增益(HZ)", required = true)
    private double freqHz;

    @ApiModelProperty(value="目标高度，默认值1000")
    private double targetHeight = 1000;

    @ApiModelProperty(value = "最大值",required = true)
    private double maxRange;

    @ApiModelProperty(value = "最小场强,dbm,默认-90dbm")
    private double minPower = -90;
}


