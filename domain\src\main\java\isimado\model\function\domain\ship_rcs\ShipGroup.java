package isimado.model.function.domain.ship_rcs;

import jsim.pro.sensor.antenna.AesEMAntenna;
import jsim.pro.sensor.signature.AesStandardRadarSignature;
import jsim.utils.ut.UtEntity;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ShipGroup {

    private List<ShipEntity> group = new ArrayList<>();

    private double azimuth;

    private double elevation;

    private double range;

    private AesStandardRadarSignature signature;

    public ShipGroup(double azimuth,double elevation, double range) {
        this.azimuth = azimuth;
        this.elevation = elevation;
        this.range = range;
    }

    public void addShip(ShipEntity ship) {
        group.add(ship);
    }

    public double getRCS(RCSService rcsService, RadarEntity radar, double freq){
        //TODO 计算RCS
        double rcs = 0;
        for (ShipEntity shipEntity : group) {
            UtEntity tgt = shipEntity.getUtEntity();
            double[] tgtToXmtrLocationWCS = new double[3];
            radar.getUtEntity().GetRelativeLocationWCS(tgt,tgtToXmtrLocationWCS);
            double[] computeAspect = tgt.ComputeAspect(tgtToXmtrLocationWCS, 0, 0);
            double aTgtToXmtrAz = computeAspect[0];
            double aTgtToXmtrEl = computeAspect[1];
            rcs += rcsService.getSignature("default", 0, freq, aTgtToXmtrAz, aTgtToXmtrEl,
                    aTgtToXmtrAz, aTgtToXmtrEl);
        }

        return rcs;
    }

    public double getRCS(){
        return group.size()*0.15;
    }

    public double getRange() {
        return range;
    }
}
