package isimado.model.function.domain.service.impl;

import imodel.jsim.function.common.FAASTwoWayInteraction;
import imodel.jsim.function.common.FAAS_ITU_Attenuation;
import imodel.jsim.function.common.JammerList;
import imodel.jsim.function.common.Target;
import isimado.framework.util.JsonUtils;
import isimado.model.function.domain.service.ShipRCSService;
import isimado.model.function.domain.ship_rcs.*;
import jsim.basic.utils.MoverUtils;
import jsim.pro.enviorment.AesEnvironment;
import jsim.pro.sensor.antenna.AesStandardAntennaPattern;
import jsim.pro.utils.UtAtmosphere;
import jsim.utils.ut.UtMath;
import jsim.utils.ut.UtVec3d;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ShipRCSServiceImpl implements ShipRCSService {

    @Resource
    private AesEnvironment aesEnvironment;

    private Thread thread;

    @Value("${service.ship_rcs.radar.angleResolution}")
    private double angleResolution;

    @Value("${service.ship_rcs.radar.frequency}")
    private double frequency;

    @Value("${service.ship_rcs.radar.power}")
    private double power;

    @Value("${service.ship_rcs.radar.noisePower}")
    private double noisePower;

    @Value("${service.ship_rcs.radar.detectionThreshold}")
    private double detectionThreshold;

    @Value("${service.ship_rcs.radar.antHeight}")
    private double antHeight;

    @Value("${service.ship_rcs.radar.bandWidth}")
    private double bandWidth;

    @Value("${service.ship_rcs.radar.pulseWidth}")
    private double pulseWidth;

    @Value("${service.ship_rcs.radar.pulseCompressionRatio}")
    private double pulseCompressionRatio;

    @Value("${service.ship_rcs.radar.pulseRepetitionFrequency}")
    private double pulseRepetitionFrequency;

    @Resource
    private AesStandardAntennaPattern pattern;

    private SituationResponse response =null;

    private final Object lock = new Object();

    private double rangeResolution;
    @Resource
    private RCSService rcsService;


    @PostConstruct
    public void init(){
        double processedPulseWidth = pulseWidth / pulseCompressionRatio;
        rangeResolution = (UtMath.cLIGHT_SPEED * processedPulseWidth) / 2.0;
        thread = new Thread(()->{
            updateLocation();
        });
        thread.start();
    }

    @Override
    public List<TrackList> attemptDetect(RadarDetectRequest request) {
        List<TrackList> results = new ArrayList<>();
        for (RadarStatus radarStatus : request.getRadarList()) {
            List<ShipGroup> shipGroups = new ArrayList<>();
            RadarEntity radar = new RadarEntity(radarStatus, pattern);
            for (ShipStatus shipStatus : request.getShipList()) {
                ShipGroup findGroup = null;
                ShipEntity ship = new ShipEntity(shipStatus);
                for (ShipGroup shipGroup : shipGroups) {
                    if (inGroup(radar, shipGroup, ship)) {
                        findGroup = shipGroup;
                    }
                }
                if (findGroup == null) {
                    double[] locationWCS = new double[3];
                    radar.getUtEntity().GetLocationWCS(locationWCS);
                    double[] tgtLocationWCS = new double[3];
                    ship.getUtEntity().GetLocationWCS(tgtLocationWCS);
                    double[] relativeLocationWCS = new double[3];
                    UtVec3d.Subtract(relativeLocationWCS, tgtLocationWCS, locationWCS);
                    double[] doubles = radar.getUtEntity().ComputeAspect(relativeLocationWCS, 0, 0);
                    double azi = doubles[0];
                    double ele = doubles[1];
                    findGroup = new ShipGroup(azi,ele, UtVec3d.Magnitude(relativeLocationWCS));
                    shipGroups.add(findGroup);
                }
                findGroup.addShip(ship);
            }
            List<Track> tracks = attemptDetect(radar, shipGroups);
            TrackList result = new TrackList();
            result.setEntityId(radar.getEntityId());
            result.setTracks(tracks);
            results.add(result);
        }
        synchronized (lock) {
            EnvironmentStatus environmentStatus = request.getEnvironmentStatus();
            if (environmentStatus != null) {
                if (environmentStatus.getWindSpeed() !=null) {
                    aesEnvironment.mWindSpeed = environmentStatus.getWindSpeed();
                }
                if (environmentStatus.getWindDirection() != null) {
                    aesEnvironment.mWindDirection = Math.toRadians(environmentStatus.getWindDirection());
                }
                if (environmentStatus.getSeaState() !=null) {
                    aesEnvironment.mSeaState = AesEnvironment.SeaState.from(environmentStatus.getSeaState());
                }
                if (environmentStatus.getRainRate() !=null) {
                    aesEnvironment.mRainUpperAlt = 5000;
                    aesEnvironment.mRainRate = environmentStatus.getRainRate();
                }
            }


            response = new SituationResponse();
            response.setTimeStamp(System.currentTimeMillis());
            response.setTrackList(results);
            response.setRadarStatusList(request.getRadarList());
            response.setShipStatusList(request.getShipList());
        }
        return results;
    }

    public void updateLocation(){
        while (true) {
            try {
                if (response != null) {
                    synchronized (lock) {
                        long timeStamp = response.getTimeStamp();
                        long now = System.currentTimeMillis();
                        double secs = (now - timeStamp) / 1000d;
                        if (secs > 1) {
                            for (ShipStatus shipStatus : response.getShipStatusList()) {
                                double speed = shipStatus.getSpeed();
                                double[] move = MoverUtils.move(shipStatus.getLocationLLA().getLatitude(), shipStatus.getLocationLLA().getLongitude(),
                                        Math.toRadians(shipStatus.getHeading()), speed, secs);
                                shipStatus.getLocationLLA().setLatitude(move[1]);
                                shipStatus.getLocationLLA().setLongitude(move[0]);
                            }
                            RadarDetectRequest request = new RadarDetectRequest();
                            request.setRadarList(response.getRadarStatusList());
                            request.setShipList(response.getShipStatusList());
                            attemptDetect(request);
                        }
                    }
                }

                Thread.sleep(1000);
            } catch (RuntimeException e) {
                throw new RuntimeException(e);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    @Override
    public String getSituation() {
        synchronized (lock) {
            return JsonUtils.toJson(response);
        }
    }

    @Override
    public double[][] compute2DRange(RadarStatus radarStatus) {
        FAAS_ITU_Attenuation faasItuAttenuation = new FAAS_ITU_Attenuation(UtAtmosphere.CreateNewAtmosphereTable(),
                frequency, 0, aesEnvironment);
        Target target = new Target();
        target.setRcs(1);
        RadarEntity radar = new RadarEntity(radarStatus, pattern);
        double[] locationLLA = radar.getUtEntity().GetLocationLLA();
        FAASTwoWayInteraction interaction = new FAASTwoWayInteraction(power,frequency,Math.pow(10, noisePower*0.1)/1000,
                radar.getAntenna(),Math.pow(10, detectionThreshold*0.1),locationLLA,
                Math.toRadians(0),
                Math.toRadians(0),antHeight,-1
                ,target,faasItuAttenuation);
        return interaction.compute2DRange(0, new JammerList());
    }

    @Override
    public double[][] compute3DRange(RadarStatus radarStatus) {
        FAAS_ITU_Attenuation faasItuAttenuation = new FAAS_ITU_Attenuation(UtAtmosphere.CreateNewAtmosphereTable(),
                frequency, 0, aesEnvironment);
        Target target = new Target();
        target.setRcs(10);
        RadarEntity radar = new RadarEntity(radarStatus, pattern);
        double[] locationLLA = radar.getUtEntity().GetLocationLLA();
        FAASTwoWayInteraction interaction = new FAASTwoWayInteraction(power,frequency,Math.pow(10, noisePower*0.1)/1000,
                radar.getAntenna(),Math.pow(10, detectionThreshold*0.1),locationLLA,
                Math.toRadians(0),
                Math.toRadians(0),antHeight,-1
                ,target,faasItuAttenuation);
        return interaction.compute3DRange(new JammerList());
    }

    private List<Track> attemptDetect(RadarEntity radar, List<ShipGroup> shipGroups){
        FAAS_ITU_Attenuation faasItuAttenuation = new FAAS_ITU_Attenuation(UtAtmosphere.CreateNewAtmosphereTable(),
                frequency, 0, aesEnvironment);
        List<Track> tracks = new ArrayList<>();
        for (ShipGroup shipGroup : shipGroups){
            Target target = new Target();
            target.setRcs(shipGroup.getRCS(rcsService,radar, frequency));
//            target.setRcs(shipGroup.getRCS());
            double[] locationLLA = radar.getUtEntity().GetLocationLLA();
            FAASTwoWayInteraction interaction = new FAASTwoWayInteraction(power,frequency,Math.pow(10, noisePower*0.1)/1000,
                    radar.getAntenna(),Math.pow(10, detectionThreshold*0.1),locationLLA,
                    Math.toRadians(0),
                    Math.toRadians(0),antHeight,-1
                    ,target,faasItuAttenuation);

            if ((interaction.computeLimitRange(locationLLA[2], shipGroup.getGroup().get(0).getUtEntity().mAlt)
                    > shipGroup.getRange())){
                interaction.computeRcvrJammerPower(shipGroup.getAzimuth(), shipGroup.getElevation(), new JammerList());
                interaction.setBandWidth(bandWidth);
                interaction.setPulseWidth(pulseWidth);
                interaction.setPulseCompressionRatio(pulseCompressionRatio);
                interaction.setPulseRepetitionFrequency(pulseRepetitionFrequency);
                double pulseDopplerClutter = FAAS_EMSurface_Clutter.PulseDopplerClutter(interaction, aesEnvironment,
                        shipGroup.getRange());
                boolean canDetected = interaction.attemptDetect(shipGroup.getGroup().get(0).getUtEntity(), pulseDopplerClutter);
                if (canDetected) {
                    Track track = new Track();
                    track.setEntityIds(shipGroup.getGroup().stream().map(ShipEntity::getEntityId).collect(Collectors.toList()));
                    tracks.add(track);
                }
            }
        }
        return tracks;
    }

    private boolean inGroup(RadarEntity radar, ShipGroup shipGroup, ShipEntity newTarget){
        double[] locationWCS = new double[3];
        radar.getUtEntity().GetLocationWCS(locationWCS);
        double[] tgtLocationWCS = new double[3];
        newTarget.getUtEntity().GetLocationWCS(tgtLocationWCS);

        double[] relativeLocationWCS = new double[3];
        UtVec3d.Subtract(relativeLocationWCS,tgtLocationWCS,locationWCS);
        double[] doubles = radar.getUtEntity().ComputeAspect(relativeLocationWCS, 0, 0);
        double azi = doubles[0];
        if (Math.abs(Math.toDegrees(azi)-Math.toDegrees(shipGroup.getAzimuth()))>angleResolution){
            return false;
        }

        for (ShipEntity entity : shipGroup.getGroup()) {
            entity.getUtEntity().GetLocationWCS(locationWCS);
            UtVec3d.Subtract(relativeLocationWCS,locationWCS, tgtLocationWCS);
            double distance = UtVec3d.Magnitude(relativeLocationWCS);
            if (distance<=rangeResolution){
                return true;
            }
        }
        return false;
    }
}
