package isimado.model.function.domain.service;

import isimado.model.function.domain.ship_rcs.RadarDetectRequest;
import isimado.model.function.domain.ship_rcs.RadarStatus;
import isimado.model.function.domain.ship_rcs.TrackList;

import java.util.List;


public interface ShipRCSService {

    List<TrackList> attemptDetect(RadarDetectRequest request);

    String getSituation();

    double[][] compute2DRange(RadarStatus radarStatus);

    double[][] compute3DRange(RadarStatus radarStatus);
}
