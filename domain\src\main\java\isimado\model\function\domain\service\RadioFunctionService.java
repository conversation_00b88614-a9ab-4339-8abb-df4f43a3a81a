package isimado.model.function.domain.service;

import isimado.model.function.share.entity.Radar2DRangeComputeDTO;
import isimado.model.function.share.entity.Radar3DRangeComputeDTO;
import isimado.model.function.share.entity.Radio2DRangeComputeDTO;
import isimado.model.function.share.entity.Radio3DRangeComputeDTO;

public interface RadioFunctionService {

    double[][] compute2DRange(Radio2DRangeComputeDTO rangeCompute);

    double[][] compute3DRange(Radio3DRangeComputeDTO rangeCompute);
}
