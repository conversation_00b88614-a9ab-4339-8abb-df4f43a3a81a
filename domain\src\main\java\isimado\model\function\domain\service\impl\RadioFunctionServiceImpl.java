package isimado.model.function.domain.service.impl;

import cn.hutool.core.lang.Assert;
import imodel.JSimPro.dataTypes.*;
import imodel.jsim.function.common.*;
import isimado.framework.util.IpUtils;
import isimado.jsim.data_type.AntennaParameter;
import isimado.jsim.data_type.AntennaPatternVariant;
import isimado.jsim.data_type.CircularPattern;
import isimado.jsim.data_type.CosecantPattern;
import isimado.jsim.data_type.RectangularPattern;
import isimado.jsim.data_type.*;
import isimado.model.function.share.entity.*;
import isimado.model.function.domain.service.RadarFunctionService;
import isimado.model.function.domain.service.RadioFunctionService;
import jsim.pro.enviorment.AesEnvironment;
import jsim.pro.sensor.antenna.AesStandardAntennaPattern;
import jsim.pro.utils.UtAtmosphere;
import org.springframework.stereotype.Service;

import static isimado.model.function.domain.AntennaPatternUtils.newPatternAntenna;

@Service
public class RadioFunctionServiceImpl implements RadioFunctionService {


    @Override
    public double[][] compute2DRange(Radio2DRangeComputeDTO rangeComputeDTO) {
        AesStandardAntennaPattern antennaPattern = newPatternAntenna(rangeComputeDTO.getAntennaPattern());

        AntennaParameter antennaParameter = rangeComputeDTO.getAntennaParameter();
        FAAS_Antenna antenna = new StandardAntennaPatternAntenna(antennaParameter.getAzimuth_scan_limits().getFirst(),
                antennaParameter.getAzimuth_scan_limits().getSecond(), antennaParameter.getElevation_scan_limits().getFirst(),
                antennaParameter.getElevation_scan_limits().getSecond(),antennaPattern);

        LocationLLA locationLLA = rangeComputeDTO.getLocationLLA();
        FAAS_ITU_Attenuation faasItuAttenuation = new FAAS_ITU_Attenuation(UtAtmosphere.CreateNewAtmosphereTable(),
                rangeComputeDTO.getFreqHz(), 0, new AesEnvironment());
        FAASOneWayInteraction interaction = new FAASOneWayInteraction(rangeComputeDTO.getXmtrPower(),rangeComputeDTO.getFreqHz(),
                rangeComputeDTO.getNoisePower(), antenna, rangeComputeDTO.getDetectionThread(),
                new double[]{locationLLA.getLatitude(), locationLLA.getLongitude(), locationLLA.getAltitude()},
                rangeComputeDTO.getHeading(),rangeComputeDTO.getPitch(),rangeComputeDTO.getAntennaParameter().getAntenna_height(),
                rangeComputeDTO.getMaxRange(),faasItuAttenuation);

        JammerList jammerList = new JammerList();
        for (JammerDTO jammer : rangeComputeDTO.getJammers()) {
            antennaParameter = jammer.getAntennaParameter();
            antenna = new StandardAntennaPatternAntenna(antennaParameter.getAzimuth_scan_limits().getFirst(),
                    antennaParameter.getAzimuth_scan_limits().getSecond(), antennaParameter.getElevation_scan_limits().getFirst(),
                    antennaParameter.getElevation_scan_limits().getSecond(),newPatternAntenna(jammer.getAntennaPattern()));
            locationLLA = jammer.getLocationLLA();
            FAASJammerInteraction faasJammerInteraction = new FAASJammerInteraction(jammer.getXmtrPower(),
                    jammer.getFreqHz(),
                    antenna,
                    new double[]{locationLLA.getLatitude(), locationLLA.getLongitude(), locationLLA.getAltitude()},
                    jammer.getHeading(),
                    jammer.getPitch(),
                    interaction,
                    jammer.getBandWidthEffect());
            jammerList.addJammer(faasJammerInteraction);
        }
        return interaction.compute2DRange(rangeComputeDTO.getTargetHeight(), jammerList);
    }

    @Override
    public double[][] compute3DRange(Radio3DRangeComputeDTO rangeComputeDTO) {
        Assert.notNull(rangeComputeDTO.getLocationLLA(), IpUtils.getIpAddr()+":LocationLLA不能为空");
        Assert.notNull(rangeComputeDTO.getLocationLLA().getLatitude(), IpUtils.getIpAddr()+":Latitude不能为空");
        Assert.notNull(rangeComputeDTO.getLocationLLA().getLongitude(),IpUtils.getIpAddr()+"Longitude不能为空");
        Assert.notNull(rangeComputeDTO.getLocationLLA().getAltitude(),IpUtils.getIpAddr()+"Altitude不能为空");
        AntennaPatternVariant pattern = rangeComputeDTO.getAntennaPattern();
        AesStandardAntennaPattern antennaPattern = newPatternAntenna(pattern);

        AntennaParameter antennaParameter = rangeComputeDTO.getAntennaParameter();
        FAAS_Antenna antenna = new StandardAntennaPatternAntenna(antennaParameter.getAzimuth_scan_limits().getFirst(),
                antennaParameter.getAzimuth_scan_limits().getSecond(), antennaParameter.getElevation_scan_limits().getFirst(),
                antennaParameter.getElevation_scan_limits().getSecond(),antennaPattern);

        LocationLLA locationLLA = rangeComputeDTO.getLocationLLA();
        FAAS_ITU_Attenuation faasItuAttenuation = new FAAS_ITU_Attenuation(UtAtmosphere.CreateNewAtmosphereTable(),
                rangeComputeDTO.getFreqHz(), 0, new AesEnvironment());
        FAASOneWayInteraction interaction = new FAASOneWayInteraction(rangeComputeDTO.getXmtrPower(),rangeComputeDTO.getFreqHz(),
                rangeComputeDTO.getNoisePower(), antenna, rangeComputeDTO.getDetectionThread(),
                new double[]{locationLLA.getLatitude(), locationLLA.getLongitude(), locationLLA.getAltitude()},
                rangeComputeDTO.getHeading(),rangeComputeDTO.getPitch(),
                rangeComputeDTO.getAntennaParameter().getAntenna_height(), rangeComputeDTO.getMaxRange(),faasItuAttenuation);

        JammerList jammerList = new JammerList();
        for (JammerDTO jammer : rangeComputeDTO.getJammers()) {
            antennaParameter = jammer.getAntennaParameter();
            antenna = new StandardAntennaPatternAntenna(antennaParameter.getAzimuth_scan_limits().getFirst(),
                    antennaParameter.getAzimuth_scan_limits().getSecond(), antennaParameter.getElevation_scan_limits().getFirst(),
                    antennaParameter.getElevation_scan_limits().getSecond(),newPatternAntenna(jammer.getAntennaPattern()));
            locationLLA = jammer.getLocationLLA();
            FAASJammerInteraction faasJammerInteraction = new FAASJammerInteraction(jammer.getXmtrPower(),
                    jammer.getFreqHz(),
                    antenna,
                    new double[]{locationLLA.getLatitude(), locationLLA.getLongitude(), locationLLA.getAltitude()},
                    jammer.getHeading(),
                    jammer.getPitch(),
                    interaction,
                    jammer.getBandWidthEffect());
            jammerList.addJammer(faasJammerInteraction);
        }
        return interaction.compute3DRange(jammerList);
    }

}
