package isimado.model.function.domain.ship_rcs;

import imodel.jsim.function.common.FAAS_ITU_Attenuation;
import isimado.jsim.data_type.CircularPattern;
import jsim.pro.enviorment.AesEnvironment;
import jsim.pro.sensor.antenna.AesStandardAntennaPattern;
import jsim.pro.utils.UtAtmosphere;
import jsim.pro.utils.UtAtmosphereData;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static isimado.model.function.domain.AntennaPatternUtils.newCircleAntennaPatternAntenna;

@Configuration
public class RadarProperties {

    @Value("${service.ship_rcs.radar.beamWidth}")
    private double beamWidth;
    @Value("${service.ship_rcs.radar.peakGain}")
    private double peakGain;

    @Bean
    public AesStandardAntennaPattern pattern(){
        CircularPattern circularPattern = new CircularPattern();
        circularPattern.setBeamwidth(beamWidth);
        circularPattern.setPeak_gain(peakGain);
        return newCircleAntennaPatternAntenna(circularPattern);
    }

    @Bean
    public AesEnvironment aesEnvironment(){
        AesEnvironment env = new AesEnvironment();
        env.mSeaState = AesEnvironment.SeaState.cROUGH;
        return env;
    }

    @Bean
    public UtAtmosphere utAtmosphere(){
        return new UtAtmosphere(new UtAtmosphereData(), UtAtmosphere.AtmosphereType.cHOT_DAY);
    }

}
