package isimado.model.function.domain.service.impl;

import cn.hutool.core.lang.Assert;
import imodel.jsim.function.common.*;
import isimado.framework.util.IpUtils;
import isimado.jsim.data_type.AntennaParameter;
import isimado.jsim.data_type.AntennaPatternVariant;
import isimado.jsim.data_type.LocationLLA;
import isimado.model.function.domain.service.JammerFunctionService;
import isimado.model.function.share.entity.Jammer2DRangeComputeDTO;
import isimado.model.function.share.entity.Jammer3DRangeComputeDTO;
import jsim.pro.enviorment.AesEnvironment;
import jsim.pro.sensor.antenna.AesStandardAntennaPattern;
import jsim.pro.utils.UtAtmosphere;
import org.springframework.stereotype.Service;

import static isimado.model.function.domain.AntennaPatternUtils.newPatternAntenna;

@Service
public class JammerFunctionServiceImpl implements JammerFunctionService {
    @Override
    public double[][] compute2DRange(Jammer2DRangeComputeDTO rangeComputeDTO) {
        Assert.notNull(rangeComputeDTO.getLocationLLA(), IpUtils.getIpAddr()+":LocationLLA不能为空");
        Assert.notNull(rangeComputeDTO.getLocationLLA().getLatitude(), IpUtils.getIpAddr()+":Latitude不能为空");
        Assert.notNull(rangeComputeDTO.getLocationLLA().getLongitude(),IpUtils.getIpAddr()+"Longitude不能为空");
        Assert.notNull(rangeComputeDTO.getLocationLLA().getAltitude(),IpUtils.getIpAddr()+"Altitude不能为空");
        AntennaPatternVariant pattern = rangeComputeDTO.getAntennaPattern();
        AesStandardAntennaPattern antennaPattern = newPatternAntenna(pattern);

        AntennaParameter antennaParameter = rangeComputeDTO.getAntennaParameter();
        FAAS_Antenna antenna = new StandardAntennaPatternAntenna(antennaParameter.getAzimuth_scan_limits().getFirst(),
                antennaParameter.getAzimuth_scan_limits().getSecond(), antennaParameter.getElevation_scan_limits().getFirst(),
                antennaParameter.getElevation_scan_limits().getSecond(),antennaPattern);

        LocationLLA locationLLA = rangeComputeDTO.getLocationLLA();
        FAAS_ITU_Attenuation faasItuAttenuation = new FAAS_ITU_Attenuation(UtAtmosphere.CreateNewAtmosphereTable(),
                rangeComputeDTO.getFreqHz(), 0, new AesEnvironment());
        FAASOneWayInteraction interaction = new FAASOneWayInteraction(rangeComputeDTO.getXmtrPower(),rangeComputeDTO.getFreqHz(),
                0, antenna, 0,
                new double[]{locationLLA.getLatitude(), locationLLA.getLongitude(), locationLLA.getAltitude()},
                rangeComputeDTO.getHeading(),rangeComputeDTO.getPitch(),
                rangeComputeDTO.getAntennaParameter().getAntenna_height(), rangeComputeDTO.getMaxRange(),faasItuAttenuation);
        return interaction.computeRangeForPower2D(rangeComputeDTO.getTargetHeight(),
                Math.pow(10, rangeComputeDTO.getMinPower()*0.1)/1000);
    }

    @Override
    public double[][] compute3DRange(Jammer3DRangeComputeDTO rangeComputeDTO) {
        Assert.notNull(rangeComputeDTO.getLocationLLA(), IpUtils.getIpAddr()+":LocationLLA不能为空");
        Assert.notNull(rangeComputeDTO.getLocationLLA().getLatitude(), IpUtils.getIpAddr()+":Latitude不能为空");
        Assert.notNull(rangeComputeDTO.getLocationLLA().getLongitude(),IpUtils.getIpAddr()+"Longitude不能为空");
        Assert.notNull(rangeComputeDTO.getLocationLLA().getAltitude(),IpUtils.getIpAddr()+"Altitude不能为空");
        AntennaPatternVariant pattern = rangeComputeDTO.getAntennaPattern();
        AesStandardAntennaPattern antennaPattern = newPatternAntenna(pattern);

        AntennaParameter antennaParameter = rangeComputeDTO.getAntennaParameter();
        FAAS_Antenna antenna = new StandardAntennaPatternAntenna(antennaParameter.getAzimuth_scan_limits().getFirst(),
                antennaParameter.getAzimuth_scan_limits().getSecond(), antennaParameter.getElevation_scan_limits().getFirst(),
                antennaParameter.getElevation_scan_limits().getSecond(),antennaPattern);

        LocationLLA locationLLA = rangeComputeDTO.getLocationLLA();
        FAAS_ITU_Attenuation faasItuAttenuation = new FAAS_ITU_Attenuation(UtAtmosphere.CreateNewAtmosphereTable(),
                rangeComputeDTO.getFreqHz(), 0, new AesEnvironment());
        FAASOneWayInteraction interaction = new FAASOneWayInteraction(rangeComputeDTO.getXmtrPower(),rangeComputeDTO.getFreqHz(),
                0, antenna, 0,
                new double[]{locationLLA.getLatitude(), locationLLA.getLongitude(), locationLLA.getAltitude()},
                rangeComputeDTO.getHeading(),rangeComputeDTO.getPitch(),
                rangeComputeDTO.getAntennaParameter().getAntenna_height(), rangeComputeDTO.getMaxRange(),faasItuAttenuation);
        return interaction.computeRangeForPower3D(Math.pow(10, rangeComputeDTO.getMinPower()*0.1)/1000);
    }
}
