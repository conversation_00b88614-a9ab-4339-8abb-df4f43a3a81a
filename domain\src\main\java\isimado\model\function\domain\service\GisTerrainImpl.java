package isimado.model.function.domain.service;

import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import imodel.jsim.function.common.TerrainInterface;
import morpheus.service.gis.GisService;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class GisTerrainImpl implements TerrainInterface {

    public GisTerrainImpl(){
        Map<String, Object> configMap = new HashMap<>();
        configMap.put("app.rasterFileDir","gdal-raster");
        configMap.put("app.osmRouteUrl","");
        configMap.put("app.shapeFile","");
        Config customConfig = ConfigFactory.parseMap(configMap);
        GisService.INSTANCE.start(customConfig);
    }

    @Override
    public double queryElevation(double lon, double lat) {
        double result =  GisService.INSTANCE.queryElevation(lon, lat);
        if (Double.isNaN(result)){
            return 0d;
        }
        return result;
    }
}
