package isimado.model.function.domain.ship_rcs;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EnvironmentStatus {

    @ApiModelProperty(value = "风速，米每秒")
    private Double windSpeed;
    @ApiModelProperty(value = "风向，东北天坐标系，0度为正北，顺时针为正，逆时针为负")
    private Double windDirection;
    @ApiModelProperty(value = "海况,0-6,海面的浪高等级")
    private Integer seaState;
//    @ApiModelProperty(value = "压强")
//    private Double pressure;
//    @ApiModelProperty(value = "温度，摄氏度")
//    private Double temperature;
//    @ApiModelProperty(value = "水蒸气密度，千克每立方米")
//    private Double waterVaporDensity;
    @ApiModelProperty(value = "降雨率，米每秒")
    private Double rainRate;
}
