package isimado.model.function.domain.service.impl;

import cn.hutool.core.lang.Assert;
import imodel.jsim.function.common.*;
import isimado.framework.util.IpUtils;
import isimado.jsim.data_type.*;
import isimado.jsim.data_type.AntennaParameter;
import isimado.jsim.data_type.AntennaPatternVariant;
import isimado.model.function.domain.service.RadarFunctionService;
import isimado.model.function.share.entity.JammerDTO;
import jsim.pro.enviorment.AesEnvironment;
import jsim.pro.sensor.antenna.AesStandardAntennaPattern;
import jsim.pro.utils.UtAtmosphere;
import org.springframework.stereotype.Service;
import isimado.model.function.share.entity.Radar2DRangeComputeDTO;
import isimado.model.function.share.entity.Radar3DRangeComputeDTO;

import static isimado.model.function.domain.AntennaPatternUtils.newPatternAntenna;


@Service
public class RadarFunctionServiceImpl implements RadarFunctionService {


    @Override
    public double[][] compute2DRange(Radar2DRangeComputeDTO radar2DRangeCompute) {
        AesStandardAntennaPattern antennaPattern = newPatternAntenna(radar2DRangeCompute.getAntennaPattern());

        AntennaParameter antennaParameter = radar2DRangeCompute.getAntennaParameter();
        FAAS_Antenna antenna = new StandardAntennaPatternAntenna(antennaParameter.getAzimuth_scan_limits().getFirst(),
                antennaParameter.getAzimuth_scan_limits().getSecond(), antennaParameter.getElevation_scan_limits().getFirst(),
                antennaParameter.getElevation_scan_limits().getSecond(),antennaPattern);

        LocationLLA locationLLA = radar2DRangeCompute.getLocationLLA();
        Target target = new Target();
        target.setRcs(radar2DRangeCompute.getTargetRCS());
        FAAS_ITU_Attenuation faasItuAttenuation = new FAAS_ITU_Attenuation(UtAtmosphere.CreateNewAtmosphereTable(),
                radar2DRangeCompute.getFreqHz(), 0, new AesEnvironment());
        FAASTwoWayInteraction interaction = new FAASTwoWayInteraction(radar2DRangeCompute.getXmtrPower(),radar2DRangeCompute.getFreqHz(),
                radar2DRangeCompute.getNoisePower(), antenna, radar2DRangeCompute.getDetectionThread(),
                new double[]{locationLLA.getLatitude(), locationLLA.getLongitude(), locationLLA.getAltitude()},
                radar2DRangeCompute.getHeading(),radar2DRangeCompute.getPitch()
                ,radar2DRangeCompute.getAntennaParameter().getAntenna_height(), radar2DRangeCompute.getMaxRange()
                ,target,faasItuAttenuation);

        JammerList jammerList = new JammerList();
        for (JammerDTO jammer : radar2DRangeCompute.getJammers()) {
            antennaParameter = jammer.getAntennaParameter();
            antenna = new StandardAntennaPatternAntenna(antennaParameter.getAzimuth_scan_limits().getFirst(),
                    antennaParameter.getAzimuth_scan_limits().getSecond(), antennaParameter.getElevation_scan_limits().getFirst(),
                    antennaParameter.getElevation_scan_limits().getSecond(),newPatternAntenna(jammer.getAntennaPattern()));
            locationLLA = jammer.getLocationLLA();
            FAASJammerInteraction faasJammerInteraction = new FAASJammerInteraction(jammer.getXmtrPower(),
                    jammer.getFreqHz(),
                    antenna,
                    new double[]{locationLLA.getLatitude(), locationLLA.getLongitude(), locationLLA.getAltitude()},
                    jammer.getHeading(),
                    jammer.getPitch(),
                    interaction,
                    jammer.getBandWidthEffect());
            jammerList.addJammer(faasJammerInteraction);
        }
        return interaction.compute2DRange(radar2DRangeCompute.getTargetHeight(), jammerList);
    }

    @Override
    public double[][] compute3DRange(Radar3DRangeComputeDTO radar3DRangeCompute) {
        Assert.notNull(radar3DRangeCompute.getLocationLLA(), IpUtils.getIpAddr()+":LocationLLA不能为空");
        Assert.notNull(radar3DRangeCompute.getLocationLLA().getLatitude(), IpUtils.getIpAddr()+":Latitude不能为空");
        Assert.notNull(radar3DRangeCompute.getLocationLLA().getLongitude(),IpUtils.getIpAddr()+"Longitude不能为空");
        Assert.notNull(radar3DRangeCompute.getLocationLLA().getAltitude(),IpUtils.getIpAddr()+"Altitude不能为空");
        AntennaPatternVariant pattern = radar3DRangeCompute.getAntennaPattern();
        AesStandardAntennaPattern antennaPattern = newPatternAntenna(pattern);

        AntennaParameter antennaParameter = radar3DRangeCompute.getAntennaParameter();
        FAAS_Antenna antenna = new StandardAntennaPatternAntenna(antennaParameter.getAzimuth_scan_limits().getFirst(),
                antennaParameter.getAzimuth_scan_limits().getSecond(), antennaParameter.getElevation_scan_limits().getFirst(),
                antennaParameter.getElevation_scan_limits().getSecond(),antennaPattern);

        LocationLLA locationLLA = radar3DRangeCompute.getLocationLLA();
        Target target = new Target();
        target.setRcs(radar3DRangeCompute.getTargetRCS());
        FAAS_ITU_Attenuation faasItuAttenuation = new FAAS_ITU_Attenuation(UtAtmosphere.CreateNewAtmosphereTable(),
                radar3DRangeCompute.getFreqHz(), 0, new AesEnvironment());
        FAASTwoWayInteraction interaction = new FAASTwoWayInteraction(radar3DRangeCompute.getXmtrPower(),radar3DRangeCompute.getFreqHz(),
                radar3DRangeCompute.getNoisePower(), antenna, radar3DRangeCompute.getDetectionThread(),
                new double[]{locationLLA.getLatitude(), locationLLA.getLongitude(), locationLLA.getAltitude()},
                radar3DRangeCompute.getHeading(),radar3DRangeCompute.getPitch(),
                radar3DRangeCompute.getAntennaParameter().getAntenna_height(), radar3DRangeCompute.getMaxRange()
                ,target,faasItuAttenuation);


        JammerList jammerList = new JammerList();
        for (JammerDTO jammer : radar3DRangeCompute.getJammers()) {
            antennaParameter = jammer.getAntennaParameter();
            antenna = new StandardAntennaPatternAntenna(antennaParameter.getAzimuth_scan_limits().getFirst(),
                    antennaParameter.getAzimuth_scan_limits().getSecond(), antennaParameter.getElevation_scan_limits().getFirst(),
                    antennaParameter.getElevation_scan_limits().getSecond(),newPatternAntenna(jammer.getAntennaPattern()));
            locationLLA = jammer.getLocationLLA();
            FAASJammerInteraction faasJammerInteraction = new FAASJammerInteraction(jammer.getXmtrPower(),
                    jammer.getFreqHz(),
                    antenna,
                    new double[]{locationLLA.getLatitude(), locationLLA.getLongitude(), locationLLA.getAltitude()},
                    jammer.getHeading(),
                    jammer.getPitch(),
                    interaction,
                    jammer.getBandWidthEffect());
            jammerList.addJammer(faasJammerInteraction);
        }
        return interaction.compute3DRange(jammerList);
    }


}
