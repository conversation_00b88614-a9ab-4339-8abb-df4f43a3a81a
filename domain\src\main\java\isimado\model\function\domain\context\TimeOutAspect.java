package isimado.model.function.domain.context;

import io.swagger.annotations.ApiOperation;
import isimado.framework.util.IpUtils;
import isimado.framework.util.JsonUtils;
import isimado.framework.util.ModelJsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.concurrent.*;


/**
 * Rest请求接口切面
 *
 * <AUTHOR>
 * @Date 2022/5/29 18:17
 */
@Aspect
@Component
@Slf4j
@ConditionalOnProperty(name = "time-out", havingValue = "open")
public class TimeOutAspect {


    public TimeOutAspect(){
        log.info("-------------------开启timeOut模式-------------------");
    }


    private static LinkedBlockingQueue<Runnable> queue = new LinkedBlockingQueue<>(30);

    private static ExecutorService executor = new ThreadPoolExecutor(2, 10, 5L, TimeUnit.MILLISECONDS, queue,
            Executors.defaultThreadFactory(), new ThreadPoolExecutor.AbortPolicy());;


    @Pointcut("execution(* isimado..*Controller.*(..))")
    public void timeOutPointCut() {
    }

    @Around("timeOutPointCut()")
    public Object doAround(ProceedingJoinPoint point) throws Throwable {
        Signature signature = point.getSignature();
        Method method = ((MethodSignature) signature).getMethod();
        String name;

        ApiOperation apiOperation = method.getAnnotation(ApiOperation.class);
        if (apiOperation == null) {
            name = signature.getDeclaringType().getSimpleName() + "." + method.getName();
        } else {
            name = apiOperation.value();
        }
        RequestAttributes attrs = RequestContextHolder.getRequestAttributes();
        boolean showLog = !name.endsWith("log-hidden");
        if (showLog) {
           // log.info("[{}] Call 【{}】 start.", IpUtils.getIpAddr(), name);
        }
        Future<?> future = executor.submit(() -> {
            try {
                RequestContextHolder.setRequestAttributes(attrs);
                return point.proceed();
            }catch (Throwable e) {
                throw new RuntimeException(e);
            }
        });

        try {
            return future.get(5, TimeUnit.SECONDS);
        }catch (TimeoutException timeoutException) {
            log.info("[{}] Call 【{}】 end. timeOut param:{}", IpUtils.getIpAddr(),  name, JsonUtils.toJson(point.getArgs()));
        }catch (ExecutionException ex) {
            throw new RuntimeException(ex);
        }

        return null;

    }
}